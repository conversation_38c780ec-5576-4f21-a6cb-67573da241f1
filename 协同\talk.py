import random
import string
import time

import requests
import datetime
import json

def generate_random_string(length=16):
    # 使用 string.ascii_letters 和 string.digits 来组成可能的字符集
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def talk_test(test_streamurl):
    url = "http://192.168.221.38:5005/wechat/talkv2"
    headers = {"Content-Type": "application/json", "Authorization": "Bearer 93DEA772FAEB849F4EFCC8F2ED3E7B7B685B8B387F53E304B96249B37423B901"}
    # , "Authorization": "Bearer e8c22d6d1daf809900e22f9ad2dbb103-zwbgsoa"
    payload = {
        "sender": generate_random_string(),
        "message": "ai_combined(扩写)",
        "appguid": "43958cea-4ef2-458c-b09f-ce51ee8fa6be",
        "debug": False,
        "stream": True,
        "streamagent":True,
        "extinfo":{
            "txtcontent":"基于现在的晋升方案，扩写后续的人才管理制度进行扩写。",
            "knowledgenamelist":"测试知识库0827001,测试知识库003",
            "ckcl": [
                "0b191071-c238-425b-8162-fd4aa8cab7dc",
                "ea579c5f-cb73-448e-895f-54f5e8f50ff1",
                "a759cb3c-7d9e-4a7e-a64d-6497d6aa277b"
            ],
            "txtyhyq":"结构清晰，表达通顺"
        }
    }

    # 请求接口获取数据
    def fetch_talk_response():
        try:
            # 根据接口需求构造请求数据（这里使用空JSON）
            response = requests.post(url, json=payload, headers=headers)

            # 检查响应状态
            if response.status_code == 200:
                print(response.text)
                # print(response.json())
                return response.json()
            else:
                print(f"Failed to fetch data, HTTP status code: {response.status_code}")
                return None
        except requests.RequestException as e:
            print(f"Request failed: {e}")
            return None

    # 提取content字段的值
    def get_content_url(data):
        try:
            if 'result' in data:
                data_json = json.loads(data['result'])
                print(json.dumps(data_json,ensure_ascii=False,indent=4))
                return data_json['url']
                # for item in data['result']:
                #     return item['content']
            return None  # 如果未找到content字段
        except KeyError as e:
            print(f"KeyError: {e}")
            return None

    try:
        response_data = fetch_talk_response()
        if test_streamurl:
            if response_data:
                # 提取content字段中的URL
                content_url = get_content_url(response_data)
            print(content_url)
            # content_url = 'https://ai.ebpu.com/EbpuRobotUATTest/rest/streammsgagent/stream?newid=69650caefa114c23ba8277ee91481a55'
            # content_url = content_url.replace('https://zwfwxc.shpt.gov.cn:2000', 'http://183.192.65.215:8081')
            # content_url = 'http://218.4.136.118:8180/EpointFrame/rest/streammsgagent/stream?newid=6d559eca9f33426593d43e836962a944'
            print(content_url)

            start_time = time.time()
            print(datetime.datetime.now())
            with requests.post(content_url, json=None, headers=None, stream=True, verify=False) as response:
                print(f"Response Content-Type: {response.headers.get('Content-Type')}")
                print(f"Response Transfer-Encoding: {response.headers.get('Transfer-Encoding')}")

                if response.status_code == 200:
                    print("Connection established, streaming data...")
                    for line in response.iter_lines(decode_unicode=True):
                        if line:  # Avoid processing empty lines
                            line = json.loads(line)['result']
                            print(line, end='', flush=True)
                else:
                    print(f"Failed to connect: {response.status_code}, {response.reason}")
            print(time.time() -start_time)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    talk_test(test_streamurl=True)
